package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PendingPlanningInstructionService {

    private static final Logger logger = LogManager.getLogger(PendingPlanningInstructionService.class);

    private final CatalogRepository catalogRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final YardRepository yardRepository;
    private final MovementInstructionRepository movementInstructionRepository;
    private final EirRepository eirRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;

    @Transactional
    public List<PendingPlanningInstructionOutput> getPendingPlanningInstructions(PendingPlanningInstructionInput.Input input) {

        try {
            Catalog catOnHold = catalogRepository.findCatalogByParentAndCode("EMI", "OH").orElse(null);
            Catalog catInProgress = catalogRepository.findCatalogByParentAndCode("EMI", "IP").orElse(null);
            Catalog catToBeAttended = catalogRepository.findCatalogByParentAndCode("EMI", "DP").orElse(null);

            List<Integer> statusIds = List.of(
                    catOnHold.getId()!=null ? catOnHold.getId() : null,
                    catInProgress.getId()!=null ? catInProgress.getId() : null,
                    catToBeAttended.getId()!=null ? catToBeAttended.getId() : null
            );

            List<String> aliases = List.of("sd1_equipment_category_container", "43081", "43083");
            List<Object[]> results = catalogRepository.findIdsByAliases(aliases);
            Map<String, Integer> aliasToId = new HashMap<>();
            for (Object[] result : results) {
                aliasToId.put((String) result[0], (Integer) result[1]);
            }
            Map<String, Integer> catalogIds = new HashMap<>();
            catalogIds.put("isContainer", aliasToId.get("sd1_equipment_category_container"));
            catalogIds.put("isGateOut", aliasToId.get("43083"));
            catalogIds.put("isEmpty", aliasToId.get("43081"));
            logger.info("Catalog IDs: {}", catalogIds);


            Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitIdByBusinessUnitId(input.getLocalBusinessUnitId());
            Integer yardId = yardRepository.findYardIdByBusinessUnitId(input.getLocalBusinessUnitId());

            List<PendingMovementInstructionDTO> pendingMovementInstructions = movementInstructionRepository.findPendingMovementInstructions(statusIds, input.getContainerNumber(), yardId);

            List<Integer> containerIds = pendingMovementInstructions.stream()
                    .map(PendingMovementInstructionDTO::getContainerId)
                    .toList();

            List<ContainerInfoDTO> containerInfo = movementInstructionRepository.findContainerInfoByIds(containerIds, subBusinessUnitId);

            Map<Integer, Integer> eirIdToCatEmptyFullId = eirRepository.findEmptyFullIdsByEirIds(
                    containerInfo.stream()
                            .map(ContainerInfoDTO::getLastGateInEirId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .toList()
            ).stream().collect(Collectors.toMap(
                    obj -> (Integer) obj[0],
                    obj -> (Integer) obj[1]
            ));

            for (ContainerInfoDTO container : containerInfo) {
                Integer eirId = container.getLastGateInEirId();
                if (eirId != null && eirIdToCatEmptyFullId.containsKey(eirId)) {
                    container.setCatEmptyFullId(eirIdToCatEmptyFullId.get(eirId));
                }
            }

            updateContainerInfoWithRestrictionReasons(containerInfo, subBusinessUnitId);

            updateContainerInfoWithLastPreallocationBookingNumber(containerInfo, subBusinessUnitId, catalogIds);

            List<MovementInformationDTO> movementsInformation = createMovementsInformation(pendingMovementInstructions);

            updateMovementsInformationWithReferenceEirIds(movementsInformation);














        }
        catch (Exception e) {
            logger.error("Error fetching pending planning instructions: ", e);
        }

        return null;
    }

    private void updateContainerInfoWithRestrictionReasons(List<ContainerInfoDTO> containerInfo, Integer subBusinessUnitId) {
        if (containerInfo == null || containerInfo.isEmpty()) {
            return;
        }

        List<ContainerInfoDTO> containersWithEmptyFullId = containerInfo.stream()
                .filter(container -> container.getCatEmptyFullId() != null)
                .toList();

        if (containersWithEmptyFullId.isEmpty()) {
            logger.debug("No containers with cat_empty_full_id found, skipping restriction reasons update");
            return;
        }

        Map<Integer, List<ContainerInfoDTO>> containersByEmptyFullId = containersWithEmptyFullId.stream()
                .collect(Collectors.groupingBy(ContainerInfoDTO::getCatEmptyFullId));

        for (Map.Entry<Integer, List<ContainerInfoDTO>> entry : containersByEmptyFullId.entrySet()) {
            Integer catEmptyFullId = entry.getKey();
            List<ContainerInfoDTO> containers = entry.getValue();

            List<Integer> containerIds = containers.stream()
                    .map(ContainerInfoDTO::getContainerId)
                    .toList();

            List<Object[]> restrictions = containerRestrictionRepository.findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
                    Set.copyOf(containerIds), Set.of(subBusinessUnitId), Set.of(catEmptyFullId));

            Map<Integer, String> containerToRestrictions = restrictions.stream()
                    .collect(Collectors.toMap(
                            result -> (Integer) result[1],
                            result -> (String) result[3],
                            (existing, replacement) -> existing
                    ));

            for (ContainerInfoDTO container : containers) {
                String restrictionReasons = containerToRestrictions.get(container.getContainerId());
                if (restrictionReasons != null && !restrictionReasons.trim().isEmpty()) {
                    container.setRestrictionReasons(restrictionReasons.trim());
                    logger.debug("Updated container {} with restriction reasons: {}",
                            container.getContainerId(), restrictionReasons);
                }
            }
        }

        int updatedCount = containerInfo.stream()
                .mapToInt(c -> c.getRestrictionReasons() != null && !c.getRestrictionReasons().isEmpty() ? 1 : 0)
                .sum();
        logger.info("Updated {} containers with restriction reasons", updatedCount);
    }

    private void updateContainerInfoWithLastPreallocationBookingNumber(List<ContainerInfoDTO> containerInfo,
                                                                        Integer subBusinessUnitId,
                                                                        Map<String, Integer> catalogIds) {
        if (containerInfo == null || containerInfo.isEmpty()) {
            return;
        }

        List<Integer> containerIds = containerInfo.stream()
                .map(ContainerInfoDTO::getContainerId)
                .toList();

        if (containerIds.isEmpty()) {
            logger.debug("No container IDs found, skipping preallocation booking number update");
            return;
        }

        Integer isGateOut = catalogIds.get("isGateOut");
        Integer isEmpty = catalogIds.get("isEmpty");

        if (isGateOut == null || isEmpty == null) {
            logger.warn("Missing catalog IDs for gate out ({}) or empty ({}), skipping preallocation booking number update",
                    isGateOut, isEmpty);
            return;
        }

        List<Object[]> preallocationResults = containerPreassignmentRepository.findLatestPreallocationBookingNumbers(
                containerIds, subBusinessUnitId, isGateOut, isEmpty);

        Map<Integer, String> containerToBookingNumber = preallocationResults.stream()
                .collect(Collectors.toMap(
                        result -> (Integer) result[0],
                        result -> (String) result[1],
                        (existing, replacement) -> existing
                ));

        for (ContainerInfoDTO container : containerInfo) {
            String bookingNumber = containerToBookingNumber.get(container.getContainerId());
            if (bookingNumber != null && !bookingNumber.trim().isEmpty()) {
                container.setLastPreallocationBookingNumber(bookingNumber.trim());
                logger.debug("Updated container {} with last preallocation booking number: {}",
                        container.getContainerId(), bookingNumber);
            }
        }

        int updatedCount = containerInfo.stream()
                .mapToInt(c -> c.getLastPreallocationBookingNumber() != null && !c.getLastPreallocationBookingNumber().isEmpty() ? 1 : 0)
                .sum();
        logger.info("Updated {} containers with last preallocation booking numbers", updatedCount);
    }

    private List<MovementInformationDTO> createMovementsInformation(List<PendingMovementInstructionDTO> pendingMovementInstructions) {
        if (pendingMovementInstructions == null || pendingMovementInstructions.isEmpty()) {
            logger.debug("No pending movement instructions found, returning empty movements information");
            return List.of();
        }

        List<Integer> movementInstructionIds = pendingMovementInstructions.stream()
                .map(PendingMovementInstructionDTO::getMovementInstructionId)
                .toList();

        List<Object[]> movementInfoResults = movementInstructionRepository.findMovementInformationWithWorkQueueResources(movementInstructionIds);

        List<MovementInformationDTO> movementsInformation = movementInfoResults.stream()
                .map(result -> new MovementInformationDTO(
                        (Integer) result[0],
                        (Integer) result[1],
                        (Integer) result[2],
                        (Integer) result[3],
                        null
                ))
                .toList();

        logger.info("Created {} movement information records from {} pending instructions",
                movementsInformation.size(), pendingMovementInstructions.size());

        return movementsInformation;
    }

    private void updateMovementsInformationWithReferenceEirIds(List<MovementInformationDTO> movementsInformation) {
        if (movementsInformation == null || movementsInformation.isEmpty()) {
            logger.debug("No movements information found, skipping reference EIR ID update");
            return;
        }

        List<Integer> eirIds = movementsInformation.stream()
                .map(MovementInformationDTO::getEirId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (eirIds.isEmpty()) {
            logger.debug("No EIR IDs found in movements information, skipping reference EIR ID update");
            return;
        }

        List<Object[]> referenceEirResults = movementInstructionRepository.findLatestReferenceEirIds(eirIds);

        Map<Integer, Integer> eirToReferenceEirMap = referenceEirResults.stream()
                .collect(Collectors.toMap(
                        result -> (Integer) result[0],
                        result -> (Integer) result[1],
                        (existing, replacement) -> existing
                ));

        for (MovementInformationDTO movement : movementsInformation) {
            Integer eirId = movement.getEirId();
            if (eirId != null && eirToReferenceEirMap.containsKey(eirId)) {
                Integer referenceEirId = eirToReferenceEirMap.get(eirId);
                movement.setReferenceEirId(referenceEirId);
                logger.debug("Updated movement {} with reference EIR ID {} for EIR {}",
                        movement.getMovementInstructionId(), referenceEirId, eirId);
            }
        }

        int updatedCount = (int) movementsInformation.stream()
                .mapToInt(m -> m.getReferenceEirId() != null ? 1 : 0)
                .sum();
        logger.info("Updated {} movements with reference EIR IDs", updatedCount);
    }

    @Data
    @AllArgsConstructor
    public static class PendingMovementInstructionDTO {
        private Integer movementInstructionId;
        private Integer containerId;
        private String containerNumber;
    }

    @Data
    @AllArgsConstructor
    public static class ContainerInfoDTO {
        private Integer containerId;
        private String containerNumber;
        private Integer containerSizeId;
        private String containerSizeCode;
        private Integer containerFamilyId;
        private String containerFamilyDescription;
        private String containerGradeDescription;
        private Integer containerTypeId;
        private String containerTypeDescription;
        private Integer containerGradeId;
        private Integer lastGateInEirId;
        private Integer catEmptyFullId;
        private String restrictionReasons;
        private String lastPreallocationBookingNumber;
    }

    @Data
    @AllArgsConstructor
    public static class MovementInformationDTO {
        private Integer containerId;
        private Integer movementInstructionId;
        private Integer workQueueResourceId;
        private Integer eirId;
        private Integer referenceEirId;
    }

}
